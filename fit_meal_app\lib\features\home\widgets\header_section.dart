import 'package:flutter/material.dart';

class HeaderSection extends StatelessWidget {
  final bool hasNotification; // Bildirim durumu
  final VoidCallback? onNotificationTap; // Bildirim tıklama callback'i

  const HeaderSection({
    super.key, 
    this.hasNotification = false, // Default: bildirim yok
    this.onNotificationTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(18, 12, 18, 0),
      child: Column(
        children: [
          // Header with menu toggle and notification
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Menu Icon - PNG asset
              GestureDetector(
                onTap: () {
                  // TODO: Sidebar açma fonksiyonu
                  print('Menu tapped');
                },
                child: SizedBox(
                  width: 20,
                  height: 16,
                  child: Image.asset(
                    'assets/icons/menu_icon.png',
                    width: 20,
                    height: 16,
                    fit: BoxFit.contain,
                  ),
                ),
              ),
              // Notification Icon - Dynamic PNG based on state
              GestureDetector(
                onTap: () {
                  onNotificationTap?.call(); // Callback'i çağır
                  print('Notification tapped - hasNotification: $hasNotification');
                },
                child: SizedBox(
                  width: 16,
                  height: 18,
                  child: hasNotification 
                      ? Image.asset(
                          'assets/icons/Notification_icon.png', // Bildirim var
                          width: 16,
                          height: 18,
                          fit: BoxFit.contain,
                        )
                      : ColorFiltered(
                          colorFilter: const ColorFilter.mode(
                            Color(0xFF3A4750), // Menu icon ile aynı renk
                            BlendMode.srcIn,
                          ),
                          child: Image.asset(
                            'assets/icons/Vector.png', // Bildirim yok
                            width: 16,
                            height: 18,
                            fit: BoxFit.contain,
                          ),
                        ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 22),
          // Greeting section
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                width: double.infinity,
                child: const Text(
                  'Hello, Good Morning',
                  style: TextStyle(
                    fontFamily: 'Montserrat',
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF3A4750),
                  ),
                ),
              ),
              const SizedBox(height: 2),
              SizedBox(
                width: double.infinity,
                child: const Text(
                  'John willes!',
                  style: TextStyle(
                    fontFamily: 'Montserrat',
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF191919),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
} 