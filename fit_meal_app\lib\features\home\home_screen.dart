import 'package:flutter/material.dart';

// Import all widget sections
import 'widgets/header_section.dart';
import 'widgets/search_section.dart';
import 'widgets/goal_section.dart';
import 'widgets/banner_section.dart';
import 'widgets/category_section.dart';
import 'widgets/popular_exercise_section.dart';
import 'widgets/meal_plans_section.dart';
import 'widgets/additional_exercise_section.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  bool hasNotification = false; // Başlangıçta bildirim yok

  void toggleNotification() {
    setState(() {
      hasNotification = !hasNotification;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFCFCFC),
      body: SafeArea(
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              HeaderSection(
                hasNotification: hasNotification,
                onNotificationTap: toggleNotification, // Test için callback
              ),
              const SizedBox(height: 24),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 18),
                child: SearchSection(),
              ),
              const SizedBox(height: 32),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 18),
                child: BannerSection(),
              ),
              const SizedBox(height: 24),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 18),
                child: GoalSection(),
              ),
              const SizedBox(height: 32),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 18),
                child: CategorySection(),
              ),
              const SizedBox(height: 24),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 18),
                child: Divider(
                  color: Color(0xFFE5E5E5),
                  thickness: 1,
                  height: 1,
                ),
              ),
              const SizedBox(height: 10),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 18),
                child: PopularExerciseSection(),
              ),
              const SizedBox(height: 10),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 18),
                child: Divider(
                  color: Color(0xFFE5E5E5),
                  thickness: 1,
                  height: 1,
                ),
              ),
              const SizedBox(height: 10),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 18),
                child: MealPlansSection(),
              ),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 18),
                child: Divider(
                  color: Color(0xFFE5E5E5),
                  thickness: 1,
                  height: 1,
                ),
              ),
              const SizedBox(height: 10),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 18),
                child: AdditionalExerciseSection(),
              ),
              const SizedBox(height: 10),
            ],
          ),
        ),
      ),
    );
  }
}