import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:email_validator/email_validator.dart';
import 'package:country_picker/country_picker.dart';

class CustomTextField extends StatefulWidget {
  final String labelText;
  final String hintText;
  final bool isPassword;
  final bool isPhone;
  final TextEditingController controller;
  final Function(Country)? onCountryChanged;
  final TextInputFormatter? phoneFormatter;

  const CustomTextField({
    super.key,
    required this.labelText,
    required this.hintText,
    required this.controller,
    this.isPassword = false,
    this.isPhone = false,
    this.onCountryChanged,
    this.phoneFormatter,
  });

  @override
  State<CustomTextField> createState() => _CustomTextFieldState();
}

class _CustomTextFieldState extends State<CustomTextField> {
  bool _isPasswordVisible = false;
  bool _isEmailValid = false;
  Country _selectedCountry = Country.parse('TR');

  @override
  void initState() {
    super.initState();
    _isPasswordVisible = !widget.isPassword;
    if (!widget.isPassword && !widget.isPhone) {
      widget.controller.addListener(_validateEmail);
    }
  }
  
  @override
  void dispose() {
    if (!widget.isPassword && !widget.isPhone) {
      widget.controller.removeListener(_validateEmail);
    }
    super.dispose();
  }

  void _validateEmail() {
    final bool isValid = EmailValidator.validate(widget.controller.text);
    if (isValid != _isEmailValid) {
      setState(() {
        _isEmailValid = isValid;
      });
    }
  }

  void _showCountryPicker() {
    showCountryPicker(
      context: context,
      countryListTheme: CountryListThemeData(
        bottomSheetHeight: 500,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20.0),
          topRight: Radius.circular(20.0),
        ),
        inputDecoration: InputDecoration(
          labelText: 'Search',
          hintText: 'Start typing to search',
          prefixIcon: const Icon(Icons.search),
          border: OutlineInputBorder(
            borderSide: BorderSide(
              color: const Color(0xFF8C98A8).withOpacity(0.2),
            ),
          ),
        ),
      ),
      onSelect: (Country country) {
        setState(() {
          _selectedCountry = country;
        });
        if (widget.onCountryChanged != null) {
          widget.onCountryChanged!(country);
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.labelText,
          style: const TextStyle(
            fontFamily: 'Montserrat',
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Color(0xFF3A4750),
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: widget.controller,
          obscureText: !_isPasswordVisible,
          keyboardType: widget.isPhone ? TextInputType.phone : TextInputType.text,
          inputFormatters: widget.isPhone && widget.phoneFormatter != null
              ? [widget.phoneFormatter!]
              : null,
          decoration: InputDecoration(
            hintText: widget.hintText,
            hintStyle: const TextStyle(
              fontFamily: 'Montserrat',
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Color(0xFFC2C2C2),
            ),
            filled: true,
            fillColor: const Color(0xFFF5F5F5),
            contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(14),
              borderSide: BorderSide.none,
            ),
            prefixIcon: widget.isPhone
                ? InkWell(
                    onTap: _showCountryPicker,
                    child: Padding(
                      padding: const EdgeInsets.only(left: 12.0, right: 8.0),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(_selectedCountry.flagEmoji, style: const TextStyle(fontSize: 20)),
                          const SizedBox(width: 4),
                          Text("+${_selectedCountry.phoneCode}", style: const TextStyle(fontSize: 14, fontFamily: 'Montserrat')),
                          const Icon(Icons.arrow_drop_down, size: 20)
                        ],
                      ),
                    ),
                  )
                : null,
            suffixIcon: widget.isPassword
                ? IconButton(
                    icon: Icon(
                      _isPasswordVisible ? Icons.visibility : Icons.visibility_off,
                      color: const Color(0xFF303841),
                    ),
                    onPressed: () {
                      setState(() {
                        _isPasswordVisible = !_isPasswordVisible;
                      });
                    },
                  )
                : ( !widget.isPhone && _isEmailValid
                    ? const Icon(Icons.check, color: Color(0xFF00ADB5)) 
                    : null),
          ),
        ),
      ],
    );
  }
} 