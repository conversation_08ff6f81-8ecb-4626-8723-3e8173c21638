import 'package:flutter/material.dart';
import 'package:fit_meal_app/features/profile/widgets/profile_header.dart';
import 'package:fit_meal_app/features/profile/widgets/stats_section.dart';
import 'package:fit_meal_app/features/profile/widgets/goal_section.dart';
import 'package:fit_meal_app/features/profile/widgets/macronutrients_section.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      backgroundColor: Color(0xFFFCFCFC),
      appBar: ProfileHeader(),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(20),
        child: <PERSON><PERSON><PERSON>(
          children: [
            <PERSON><PERSON><PERSON><PERSON>(height: 30),
            StatsSection(),
            <PERSON><PERSON><PERSON><PERSON>(height: 40),
            GoalSection(),
            Sized<PERSON>ox(height: 50),
            MacronutrientsSection(),
          ],
        ),
      ),
    );
  }
} 