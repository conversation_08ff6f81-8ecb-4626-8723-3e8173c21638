{"models": {"main": {"provider": "google", "modelId": "gemini-2.5-pro", "maxTokens": 1048000, "temperature": 0.2}, "research": {"provider": "google", "modelId": "gemini-2.5-pro", "maxTokens": 8700, "temperature": 0.1}, "fallback": {"provider": "google", "modelId": "gemini-2.5-pro", "maxTokens": 1048000, "temperature": 0.1}}, "global": {"logLevel": "info", "debug": false, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Taskmaster", "ollamaBaseURL": "http://localhost:11434/api", "bedrockBaseURL": "https://bedrock.us-east-1.amazonaws.com", "defaultTag": "master", "azureOpenaiBaseURL": "https://your-endpoint.openai.azure.com/", "userId": "**********"}}