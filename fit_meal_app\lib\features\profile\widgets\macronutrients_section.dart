import 'package:flutter/material.dart';

class MacronutrientsSection extends StatelessWidget {
  const MacronutrientsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Macronutrient Goals',
          style: TextStyle(
            fontFamily: 'Montserrat',
            fontSize: 16,
            fontWeight: FontWeight.w700,
            color: Color(0xFF191919),
          ),
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _MacronutrientItem(
              iconPath: 'assets/icons/protein.png',
              label: 'Protein',
              value: '130',
            ),
            _MacronutrientItem(
              iconPath: 'assets/icons/carbs.png',
              label: 'Carbs',
              value: '235',
            ),
            _MacronutrientItem(
              iconPath: 'assets/icons/fat.png',
              label: 'Fat',
              value: '60',
            ),
          ],
        ),
      ],
    );
  }
}

class _MacronutrientItem extends StatelessWidget {
  final String iconPath;
  final String label;
  final String value;

  const _MacronutrientItem({
    required this.iconPath,
    required this.label,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: const Color(0xFFF5F5F5),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Center(
            child: Image.asset(
              iconPath,
              width: 32,
              height: 32,
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: const TextStyle(
            fontFamily: 'Montserrat',
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: Color(0xFF191919),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontFamily: 'Montserrat',
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: Color(0xFF191919),
          ),
        ),
        const SizedBox(height: 4),
        const Text(
          'Grams per day',
          style: TextStyle(
            fontFamily: 'Montserrat',
            fontSize: 10,
            fontWeight: FontWeight.w400,
            color: Color(0xFF3A4750),
          ),
        ),
      ],
    );
  }
} 