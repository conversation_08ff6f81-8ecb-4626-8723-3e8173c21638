<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <!-- Export method: app-store, ad-hoc, development, enterprise -->
    <key>method</key>
    <string>app-store</string>
    
    <!-- Upload symbols for crash reporting -->
    <key>uploadBitcode</key>
    <false/>
    
    <!-- Upload symbols for crash reporting -->
    <key>uploadSymbols</key>
    <true/>
    
    <!-- Compile bitcode -->
    <key>compileBitcode</key>
    <false/>
    
    <!-- Strip Swift symbols -->
    <key>stripSwiftSymbols</key>
    <true/>
    
    <!-- Team ID for signing -->
    <key>teamID</key>
    <string>$(DEVELOPMENT_TEAM)</string>
    
    <!-- Signing style -->
    <key>signingStyle</key>
    <string>automatic</string>
    
    <!-- Signing certificate -->
    <key>signingCertificate</key>
    <string>iPhone Distribution</string>
    
    <!-- Provisioning profiles per bundle ID -->
    <key>provisioningProfiles</key>
    <dict>
        <key>com.fitmeal.app</key>
        <string>$(PROVISIONING_PROFILE_SPECIFIER)</string>
    </dict>
    
    <!-- Destination for export -->
    <key>destination</key>
    <string>export</string>
    
    <!-- Thin for all device variants -->
    <key>thinning</key>
    <string>&lt;none&gt;</string>
</dict>
</plist> 