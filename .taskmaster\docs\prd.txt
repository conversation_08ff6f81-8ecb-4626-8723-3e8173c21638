# Overview
FitMeal is a cross-platform wellness application that helps users achieve their fitness and nutrition goals through personalised workout programs and meal plans. Built with Flutter for iOS & Android and powered by Supabase (PostgreSQL + Realtime), it targets health-conscious individuals who want a single, convenient solution for tracking exercise, diet, and progress.

It solves the fragmentation problem between fitness apps (workouts) and calorie trackers (meals) by combining them in one intuitive experience that syncs data in real-time across devices.

# Core Features
- **Onboarding & Personalisation**  
  - Collects gender, age, weight, height, activity level, and goal (lose, gain, maintain).  
  - Stores baseline metrics in Supabase to tailor workouts & calories.
- **Authentication & Profile**  
  - Email/password & OAuth (Apple, Google).  
  - Profile editing, avatar upload (Supabase Storage).
- **Workout Module**  
  - Category list (Yoga, Gym, Cardio, Stretch, Full Body).  
  - Exercise detail pages with video/GIF, reps, sets, rest timer.  
  - Training programs sorted by Beginner / Intermediate / Advanced.
- **Meal Plan Module**  
  - Breakfast / Lunch / Dinner tabs with recipe cards.  
  - Macronutrient breakdown, kcal count, cooking time.  
  - Favourites & shopping list export.
- **Progress Tracking**  
  - Weight, BMI, calorie intake & workout completion streaks.  
  - Charts powered by Supabase Realtime; offline cache with Hive.
- **Notifications & Reminders**  
  - Push notifications (FCM/APNS) for workout time, meal reminders.  
  - In-app streak encouragement.
- **Subscription / Premium Unlock**  
  - Paywall & plans (monthly, yearly) using RevenueCat.  
  - Unlock premium workouts, advanced analytics.
- **Dark & Light Themes**  
  - Full parity screens for accessibility & battery saving.

# User Experience
## Personas
1. **Fitness Newbie (Emma 24)** — needs guided plans, easy UX.  
2. **Busy Professional (Ali 34)** — limited time, wants quick workouts & meal prep ideas.  
3. **Health Enthusiast (Sara 28)** — demands detailed metrics & variety.

## Key User Flows
- First launch → Onboarding → Personalised plan → Daily dashboard.  
- Select workout → Watch demo → Start timer → Complete → Progress update.  
- Browse meal plan → View recipe → Mark cooked → Calories logged.

## UI/UX Considerations
- One-hand navigation, bottom tabs.  
- Consistent card components & 8-pt spacing.  
- Offline friendly; show skeleton loaders for network fetch.  
- WCAG 2.1 AA contrast ratios.

# Technical Architecture
- **Frontend**: Flutter 3.x with Riverpod state management, go_router navigation, Hive for offline cache, Firebase Messaging.
- **Backend**: Supabase 2.x  
  - PostgreSQL database (row level security).  
  - Supabase Auth, Storage (media), Edge Functions (serverless)
- **Data Models**:  
  - `users` (id, profile, metrics)  
  - `exercises` (id, category, media_url, difficulty, kcal)  
  - `workout_programs` (id, level, duration, exercises[])  
  - `meals` (id, title, macros, ingredients, steps)  
  - `meal_plans` (id, day, meal_ids[])  
  - `progress` (user_id, date, weight, calories_in, calories_out)
- **APIs & Integrations**: Supabase client SDK, RevenueCat, FCM, Crashlytics.
- **Infrastructure**: Supabase cloud, Vercel Edge functions fallback; CI/CD via GitHub Actions → Test → Build → Deploy to stores.

# Development Roadmap
## Phase 1 – MVP
- Flutter project setup, CI/CD.  
- Supabase schema & RLS policies.  
- Auth + Onboarding flow.  
- Home dashboard with workout & meal cards.  
- Workout player (timer, progress).  
- Static meal plan list.

## Phase 2 – Enhanced Tracking
- Realtime progress charts.  
- Meal plan generator w/ macros.  
- Favorites & search.  
- Notifications & reminders.  
- Dark mode parity.

## Phase 3 – Monetisation & Growth
- Subscription paywall integration.  
- Premium content gating.  
- Social sharing & friend leaderboards.  
- AI-powered suggestions (OpenAI) for workouts/meals.  
- Wearable integration (Apple Health / Google Fit).

# Logical Dependency Chain
1. Design system & theme setup → shared components.  
2. Supabase project + schema → Auth implemented.  
3. Onboarding flow → store user metrics.  
4. Home dashboard scaffolding.  
5. Workout module (exercise player) → progress tracking.  
6. Meal module → macro calculations.  
7. Notifications & offline cache.  
8. Premium/paywall → RevenueCat.  
9. Advanced analytics & integrations.

# Risks and Mitigations
- **Flutter performance on low-end devices** → Use const widgets, cache images, lazy load.  
- **Supabase rate limits** → Implement local cache & pagination.  
- **Subscription rejection (App Store)** → Follow Apple guidelines, use RevenueCat compliantly.  
- **Data accuracy (macros)** → Source verified nutrition DB.

# Appendix
- Component library: Material 3-based custom kit.  
- Research: WHO fitness guidelines, Harvard Food Pyramid.  
- Supabase docs references. 