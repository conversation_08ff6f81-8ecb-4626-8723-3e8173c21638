import 'package:flutter/material.dart';

class Password<PERSON><PERSON>y<PERSON>hecker extends StatelessWidget {
  final bool has8Chars;
  final bool hasUppercase;
  final bool hasLowercase;
  final bool hasNumber;

  const PasswordPolicyChecker({
    super.key,
    required this.has8Chars,
    required this.hasUppercase,
    required this.hasLowercase,
    required this.hasNumber,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _PolicyRow(text: 'At least 8 characters', isMet: has8Chars),
        const SizedBox(height: 4),
        _PolicyRow(text: 'Contains an uppercase letter', isMet: hasUppercase),
        const SizedBox(height: 4),
        _PolicyRow(text: 'Contains a lowercase letter', isMet: hasLowercase),
        const SizedBox(height: 4),
        _PolicyRow(text: 'Contains a number', isMet: hasNumber),
      ],
    );
  }
}

class _PolicyRow extends StatelessWidget {
  final String text;
  final bool isMet;

  const _PolicyRow({required this.text, required this.isMet});

  @override
  Widget build(BuildContext context) {
    final color = isMet ? Colors.green : Colors.grey;
    return Row(
      children: [
        Icon(
          isMet ? Icons.check_circle : Icons.radio_button_unchecked,
          color: color,
          size: 16,
        ),
        const SizedBox(width: 8),
        Text(
          text,
          style: TextStyle(
            color: color,
            fontFamily: 'Montserrat',
            fontSize: 12,
          ),
        ),
      ],
    );
  }
} 