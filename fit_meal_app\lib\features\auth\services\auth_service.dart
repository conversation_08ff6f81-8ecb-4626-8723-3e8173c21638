import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_libphonenumber/flutter_libphonenumber.dart';

// This provider will be used to access the AuthService from the UI.
final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService(Supabase.instance.client);
});

class AuthService {
  final GoTrueClient _auth;

  AuthService(SupabaseClient client) : _auth = client.auth;

  Future<void> signInWithPassword(String email, String password) async {
    try {
      await _auth.signInWithPassword(
        email: email,
        password: password,
      );
    } on AuthException {
      // It's better to handle specific errors and show user-friendly messages.
      // For now, we'll just rethrow the exception.
      rethrow;
    } catch (e) {
      // Handle other potential errors
      rethrow;
    }
  }

  Future<void> signUpWithPassword(String email, String password, {String? fullName, String? phone, String? isoCode}) async {
    // International phone number validation using the new package
    if (phone != null && phone.isNotEmpty && isoCode != null) {
      try {
        await parse(phone, region: isoCode);
      } catch (e) {
        throw const AuthException('Please enter a valid phone number for the selected country.');
      }
    }

    // Email domain validation
    final validDomains = ['gmail.com', 'yahoo.com', 'outlook.com', 'hotmail.com', 'icloud.com'];
    final emailDomain = email.split('@').last;
    if (!validDomains.contains(emailDomain)) {
      throw const AuthException('Please use a valid email provider (e.g., Gmail, Outlook).');
    }

    // Password policy validation
    if (password.length < 8) {
      throw const AuthException('Password must be at least 8 characters long.');
    }
    if (!password.contains(RegExp(r'[A-Z]'))) {
      throw const AuthException('Password must contain at least one uppercase letter.');
    }
    if (!password.contains(RegExp(r'[a-z]'))) {
      throw const AuthException('Password must contain at least one lowercase letter.');
    }
    if (!password.contains(RegExp(r'[0-9]'))) {
      throw const AuthException('Password must contain at least one number.');
    }
    
    try {
      await _auth.signUp(
        email: email,
        password: password,
        data: {
          'full_name': fullName,
          'phone': phone,
        },
      );
    } on AuthException {
      rethrow;
    } catch (e) {
      rethrow;
    }
  }

  // TODO: Add methods for signOut, resetPassword, etc.
} 