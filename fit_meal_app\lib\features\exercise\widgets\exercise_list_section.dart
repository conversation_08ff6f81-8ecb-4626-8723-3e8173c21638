import 'package:flutter/material.dart';
import 'package:fit_meal_app/features/exercise/widgets/exercise_card.dart';

class ExerciseListSection extends StatelessWidget {
  final String selectedCategory;

  const ExerciseListSection({
    super.key,
    required this.selectedCategory,
  });

  @override
  Widget build(BuildContext context) {
    final exercises = _getExercisesForCategory(selectedCategory);
    
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: ListView.separated(
        itemCount: exercises.length,
        separatorBuilder: (context, index) => Column(
          children: [
            const SizedBox(height: 12),
            const Divider(
              color: Color(0xFFDDDDDD),
              thickness: 1.5,
              height: 1,
              indent: 0,
              endIndent: 0,
            ),
            const SizedBox(height: 12),
          ],
        ),
        itemBuilder: (context, index) {
          final exercise = exercises[index];
          return ExerciseCard(
            title: exercise['title']!,
            level: exercise['level']!,
            duration: exercise['duration']!,
            calories: exercise['calories']!,
            imageUrl: exercise['imageUrl']!,
          );
        },
      ),
    );
  }

  List<Map<String, String>> _getExercisesForCategory(String category) {
    switch (category) {
      case 'Cardio':
        return [
          {
            'title': 'Exercises with Jumping \nRope',
            'level': 'Beginner',
            'duration': '10 min',
            'calories': '110 kcal',
            'imageUrl': 'assets/images/jumping.png',
          },
          {
            'title': 'Exercises with Holding Jumping \nRope',
            'level': 'Beginner',
            'duration': '8 min',
            'calories': '135 kcal',
            'imageUrl': 'assets/images/jumping.png',
          },
          {
            'title': 'Exercises with Sitting \nDumbbells',
            'level': 'Beginner',
            'duration': '5 min',
            'calories': '135 kcal',
            'imageUrl': 'assets/images/sitting.png',
          },
          {
            'title': 'Exercises with Sitting \nDumbbells',
            'level': 'Beginner',
            'duration': '5 min',
            'calories': '135 kcal',
            'imageUrl': 'assets/images/sitting.png',
          },
          {
            'title': 'Exercises with Sitting \nDumbbells',
            'level': 'Beginner',
            'duration': '5 min',
            'calories': '135 kcal',
            'imageUrl': 'assets/images/sitting.png',
          },
        ];
      case 'Legs':
        return [
          {
            'title': 'Leg Press Machine',
            'level': 'Intermediate',
            'duration': '15 min',
            'calories': '180 kcal',
            'imageUrl': 'assets/images/sitting.png',
          },
          {
            'title': 'Squats with Dumbbells',
            'level': 'Beginner',
            'duration': '12 min',
            'calories': '150 kcal',
            'imageUrl': 'assets/images/sitting.png',
          },
        ];
      case 'Back':
        return [
          {
            'title': 'Pull-ups',
            'level': 'Advanced',
            'duration': '20 min',
            'calories': '200 kcal',
            'imageUrl': 'assets/images/jumping.png',
          },
          {
            'title': 'Lat Pulldown',
            'level': 'Intermediate',
            'duration': '15 min',
            'calories': '170 kcal',
            'imageUrl': 'assets/images/sitting.png',
          },
        ];
      case 'Chest':
        return [
          {
            'title': 'Bench Press',
            'level': 'Intermediate',
            'duration': '18 min',
            'calories': '190 kcal',
            'imageUrl': 'assets/images/sitting.png',
          },
          {
            'title': 'Push-ups',
            'level': 'Beginner',
            'duration': '10 min',
            'calories': '120 kcal',
            'imageUrl': 'assets/images/jumping.png',
          },
        ];
      default:
        return [];
    }
  }
} 