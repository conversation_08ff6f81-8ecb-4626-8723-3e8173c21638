import 'package:flutter/material.dart';

class GoalSection extends StatelessWidget {
  const GoalSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Select your Goal',
          style: TextStyle(
            fontFamily: 'Montserrat',
            fontSize: 16,
            fontWeight: FontWeight.w700,
            color: Color(0xFF191919),
          ),
        ),
        const SizedBox(height: 8),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              GoalChip(
                label: 'Lose Weight',
                isSelected: false,
              ),
              const SizedBox(width: 12),
              GoalChip(
                label: 'Gain Weight',
                isSelected: true,
              ),
              const SizedBox(width: 12),
              GoalChip(
                label: 'Body Building',
                isSelected: false,
              ),
              const SizedBox(width: 12),
              GoalChip(
                label: 'Healthy',
                isSelected: false,
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class GoalChip extends StatelessWidget {
  final String label;
  final bool isSelected;

  const GoalChip({
    super.key,
    required this.label,
    required this.isSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 32,
      padding: const EdgeInsets.symmetric(horizontal: 18),
      decoration: BoxDecoration(
        color: isSelected ? const Color(0xFF00ADB5) : const Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(6),
      ),
      alignment: Alignment.center,
      child: Text(
        label,
        style: const TextStyle(
          fontFamily: 'Montserrat',
          fontSize: 11,
          fontWeight: FontWeight.w500,
        ).copyWith(
          color: isSelected ? Colors.white : const Color(0xFF303841),
        ),
      ),
    );
  }
} 