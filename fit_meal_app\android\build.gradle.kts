allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

val newBuildDir: Directory = rootProject.layout.buildDirectory.dir("../../build").get()
rootProject.layout.buildDirectory.value(newBuildDir)

subprojects {
    val newSubprojectBuildDir: Directory = newBuildDir.dir(project.name)
    project.layout.buildDirectory.value(newSubprojectBuildDir)
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register<Delete>("clean") {
    delete(rootProject.layout.buildDirectory)
}

// Add this block to fix namespace issues with older packages
subprojects {
    afterEvaluate {
        if (project.plugins.hasPlugin("com.android.library")) {
            android {
                if (namespace == null) {
                    namespace = "com.fitmeal.app.${project.name.replace('-', '_')}"
                }
            }
        }
    }
}
