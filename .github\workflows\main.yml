name: Flutter CI/CD

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]

jobs:
  analyze:
    name: Code Analysis
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.29.3'
          channel: 'stable'
          
      - name: Get dependencies
        run: |
          cd fit_meal_app
          flutter pub get
          
      - name: Verify dependencies
        run: |
          cd fit_meal_app
          flutter pub deps
          
      - name: Run Flutter analyze
        run: |
          cd fit_meal_app
          flutter analyze
          
      - name: Check formatting
        run: |
          cd fit_meal_app
          dart format --set-exit-if-changed lib/

  test:
    name: Unit & Widget Tests
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.29.3'
          channel: 'stable'
          
      - name: Get dependencies
        run: |
          cd fit_meal_app
          flutter pub get
          
      - name: Run tests
        run: |
          cd fit_meal_app
          flutter test --coverage
          
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          file: fit_meal_app/coverage/lcov.info
          fail_ci_if_error: false

  build-android:
    name: Build Android APK
    runs-on: ubuntu-latest
    needs: [analyze, test]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.29.3'
          channel: 'stable'
          
      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: 'zulu'
          java-version: '11'
          
      - name: Get dependencies
        run: |
          cd fit_meal_app
          flutter pub get
          
      - name: Build APK
        run: |
          cd fit_meal_app
          flutter build apk --debug
          
      - name: Upload APK artifact
        uses: actions/upload-artifact@v4
        with:
          name: debug-apk
          path: fit_meal_app/build/app/outputs/flutter-apk/app-debug.apk

  build-android-release:
    name: Build Android Release APK
    runs-on: ubuntu-latest
    needs: [analyze, test]
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.29.3'
          channel: 'stable'
          
      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: 'zulu'
          java-version: '11'
          
      - name: Get dependencies
        run: |
          cd fit_meal_app
          flutter pub get
          
      - name: Decode keystore
        if: github.event_name != 'pull_request'
        run: |
          echo "${{ secrets.KEYSTORE_BASE64 }}" | base64 -d > fit_meal_app/android/keystore.jks
          
      - name: Create key.properties
        if: github.event_name != 'pull_request'
        run: |
          echo "storePassword=${{ secrets.KEYSTORE_PASSWORD }}" > fit_meal_app/android/key.properties
          echo "keyPassword=${{ secrets.KEY_PASSWORD }}" >> fit_meal_app/android/key.properties
          echo "keyAlias=${{ secrets.KEY_ALIAS }}" >> fit_meal_app/android/key.properties
          echo "storeFile=../keystore.jks" >> fit_meal_app/android/key.properties
          
      - name: Build Release APK
        run: |
          cd fit_meal_app
          flutter build apk --release
          
      - name: Build Release App Bundle
        run: |
          cd fit_meal_app
          flutter build appbundle --release
          
      - name: Upload Release APK
        uses: actions/upload-artifact@v4
        with:
          name: release-apk
          path: fit_meal_app/build/app/outputs/flutter-apk/app-release.apk
          
      - name: Upload Release App Bundle
        uses: actions/upload-artifact@v4
        with:
          name: release-aab
          path: fit_meal_app/build/app/outputs/bundle/release/app-release.aab

  build-ios:
    name: Build iOS Debug
    runs-on: macos-latest
    needs: [analyze, test]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.29.3'
          channel: 'stable'
          
      - name: Get dependencies
        run: |
          cd fit_meal_app
          flutter pub get
          
      - name: Build iOS (No Codesign)
        run: |
          cd fit_meal_app
          flutter build ios --no-codesign --debug

  build-ios-release:
    name: Build iOS Release IPA
    runs-on: macos-latest
    needs: [analyze, test]
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.29.3'
          channel: 'stable'
          
      - name: Get dependencies
        run: |
          cd fit_meal_app
          flutter pub get
          
      - name: Install Certificate
        if: github.event_name != 'pull_request'
        env:
          DISTRIBUTION_CERTIFICATE_BASE64: ${{ secrets.DISTRIBUTION_CERTIFICATE_BASE64 }}
          CERTIFICATE_PASSWORD: ${{ secrets.CERTIFICATE_PASSWORD }}
        run: |
          # Create certificate file
          echo "$DISTRIBUTION_CERTIFICATE_BASE64" | base64 --decode > distribution_certificate.p12
          
          # Create keychain
          security create-keychain -p "$CERTIFICATE_PASSWORD" build.keychain
          security default-keychain -s build.keychain
          security unlock-keychain -p "$CERTIFICATE_PASSWORD" build.keychain
          
          # Import certificate
          security import distribution_certificate.p12 -k build.keychain -P "$CERTIFICATE_PASSWORD" -T /usr/bin/codesign
          security set-key-partition-list -S apple-tool:,apple:,codesign: -s -k "$CERTIFICATE_PASSWORD" build.keychain
          
      - name: Install Provisioning Profile
        if: github.event_name != 'pull_request'
        env:
          PROVISIONING_PROFILE_BASE64: ${{ secrets.PROVISIONING_PROFILE_BASE64 }}
        run: |
          # Create provisioning profile
          echo "$PROVISIONING_PROFILE_BASE64" | base64 --decode > provisioning_profile.mobileprovision
          
          # Install provisioning profile
          mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles
          cp provisioning_profile.mobileprovision ~/Library/MobileDevice/Provisioning\ Profiles/
          
      - name: Build iOS Release IPA
        if: github.event_name != 'pull_request'
        run: |
          cd fit_meal_app
          flutter build ipa --release --export-options-plist=ios/ExportOptions.plist
          
      - name: Upload IPA
        if: github.event_name != 'pull_request'
        uses: actions/upload-artifact@v4
        with:
          name: release-ipa
          path: fit_meal_app/build/ios/ipa/*.ipa
          
      - name: Cleanup
        if: always() && github.event_name != 'pull_request'
        run: |
          security delete-keychain build.keychain || true
          rm -f distribution_certificate.p12 provisioning_profile.mobileprovision 