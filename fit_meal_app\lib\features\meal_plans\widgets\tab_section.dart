import 'package:flutter/material.dart';

class TabSection extends StatelessWidget {
  final String selectedTab;
  final Function(String) onTabChanged;

  const TabSection({
    super.key,
    required this.selectedTab,
    required this.onTabChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: Row(
        children: [
          _buildTab('Breakfast'),
          _buildTab('Lunch'),
          _buildTab('Dinner'),
        ],
      ),
    );
  }

  Widget _buildTab(String tabName) {
    final isSelected = selectedTab == tabName;
    
    return Expanded(
      child: GestureDetector(
        onTap: () => onTabChanged(tabName),
        child: Container(
          height: 38,
          decoration: BoxDecoration(
            color: isSelected ? const Color(0xFF00ADB5) : Colors.transparent,
            borderRadius: BorderRadius.circular(6),
          ),
          alignment: Alignment.center,
          child: Text(
            tabName,
            style: TextStyle(
              fontFamily: '<PERSON><PERSON><PERSON>',
              fontWeight: FontWeight.w600,
              fontSize: 12,
              color: isSelected ? Colors.white : const Color(0xFF3A4750),
              letterSpacing: 0,
              height: 1.22,
            ),
          ),
        ),
      ),
    );
  }
} 