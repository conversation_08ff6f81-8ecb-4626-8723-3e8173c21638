{"buildFiles": ["C:\\Users\\<USER>\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\FitMeal\\fit_meal_app\\android\\app\\.cxx\\Debug\\2o6m93z4\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\FitMeal\\fit_meal_app\\android\\app\\.cxx\\Debug\\2o6m93z4\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}